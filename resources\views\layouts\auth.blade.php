<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CATAT">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <title>{{ config('app.name', 'CATAT') }}</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Mobile Fallback CSS - Always load first -->
    <link rel="stylesheet" href="{{ asset('css/mobile-fallback.css') }}">

    @if(app()->environment('production'))
        <!-- Production: Use Vite compiled assets -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @else
        <!-- Development: Use Vite dev server or fallback -->
        @if(file_exists(public_path('hot')))
            @vite(['resources/css/app.css', 'resources/js/app.js'])
        @else
            <!-- Fallback to compiled assets if dev server not running -->
            <link rel="stylesheet" href="{{ asset('build/assets/app-DCiv33ZF.css') }}">
            <script src="{{ asset('build/assets/app-CQXzpyVp.js') }}" defer></script>
        @endif
    @endif

    <!-- Complete CSS Embedded for Mobile Compatibility -->
    <style>
        /* CSS Variables */
        :root {
            --primary: #2563eb;
            --primary-dark: #1e40af;
            --primary-light: #3b82f6;
            --secondary: #0ea5e9;
            --accent: #06b6d4;
            --white: #ffffff;
            --light: #f0f9ff;
            --gray-light: #f1f5f9;
            --gray: #94a3b8;
            --gray-dark: #475569;
            --text-primary: #1e293b;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --bg-gradient: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%);
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.12), 0 4px 10px rgba(0, 0, 0, 0.06);
            --radius: 16px;
            --radius-sm: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            text-decoration: none !important;
        }

        /* Body */
        body {
            font-family: 'Poppins', sans-serif;
            background: var(--bg-gradient);
            background-attachment: fixed;
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.6;
        }

        /* Auth Container */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            padding: 2rem 0;
            min-height: 100vh;
        }

        .auth-card {
            background: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
            text-align: center;
            color: var(--text-primary);
        }

        .auth-card h2 {
            font-size: 1.875rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .auth-card p {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0;
            margin-bottom: 1.5rem;
        }

        .auth-card .link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease-in-out;
        }

        .auth-card .link:hover {
            color: var(--secondary);
        }

        /* Forms */
        .form-group {
            margin-bottom: 1rem;
            text-align: left;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-dark);
            margin-bottom: 8px;
        }

        .form-control, input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            transition: var(--transition);
            color: #1e293b;
            font-family: 'Poppins', sans-serif;
        }

        .form-control:focus, input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-1px);
        }

        /* Buttons */
        .btn, button {
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none !important;
            font-family: 'Poppins', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .btn-primary, button[type="submit"] {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-primary:hover, button[type="submit"]:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Role Selection */
        .role-selection {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .btn-role {
            flex-grow: 1;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            background-color: #ffffff;
            color: var(--text-primary);
            font-weight: 500;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            text-align: center;
        }

        .btn-role[data-role="manager"],
        .btn-role[data-role="employee"],
        .btn-role[data-role="director"] {
            background-color: #93c5fd;
            color: #1e3a8a;
            border-color: #93c5fd;
        }

        .btn-role[data-role="manager"]:hover,
        .btn-role[data-role="employee"]:hover,
        .btn-role[data-role="director"]:hover {
            background-color: #bfdbfe;
            border-color: #bfdbfe;
        }

        .btn-role.selected {
            background-color: #3b82f6 !important;
            color: white !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 8px rgba(59, 130, 246, 0.7);
        }

        /* Error Messages */
        .text-danger {
            color: var(--danger);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .alert-danger {
            background-color: #fee2e2;
            color: var(--danger);
            border: 1px solid #fca5a5;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .auth-card {
                margin: 1rem;
                padding: 1.5rem;
            }

            .form-control, input, select, textarea {
                font-size: 16px; /* Prevent zoom on iOS */
                padding: 0.75rem;
            }

            .btn, button {
                width: 100%;
                padding: 0.875rem 1rem;
                font-size: 0.875rem;
                justify-content: center;
            }

            .role-selection {
                flex-direction: column;
                gap: 0.75rem;
            }

            .btn-role {
                width: 100%;
            }

            body {
                overflow-x: hidden;
            }
        }

        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
            .btn, button {
                min-height: 44px;
                position: relative;
                overflow: hidden;
            }

            .btn:active, button:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }

            .form-control, input, select, textarea {
                min-height: 44px;
                font-size: 16px;
            }
        }

        /* Utilities */
        .w-full { width: 100%; }
        .text-center { text-align: center; }
        .d-none { display: none; }
        .mt-1 { margin-top: 0.25rem; }
        .mb-4 { margin-bottom: 1rem; }

        /* Links */
        a {
            color: var(--primary);
            text-decoration: none !important;
        }

        a:hover {
            color: var(--primary-dark);
            text-decoration: none !important;
        }
    </style>
</head>
<body>
    @yield('content')

    <script>
        // Touch feedback for buttons
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn, button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function(e) {
                    this.style.transform = 'scale(0.98)';
                    this.style.opacity = '0.8';
                    this.style.transition = 'all 0.1s ease';
                });

                button.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                        this.style.opacity = '';
                        this.style.transition = '';
                    }, 150);
                });
            });

            // Improve form input focus on mobile
            const inputs = document.querySelectorAll('.form-control, input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    // Scroll input into view on mobile
                    if (window.innerWidth <= 768) {
                        setTimeout(() => {
                            this.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }, 300);
                    }
                });
            });
        });
    </script>
</body>
</html>