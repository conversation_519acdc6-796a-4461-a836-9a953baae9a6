/* Mobile Fallback CSS - Critical Styles - Force Load */
:root {
    --primary: #2563eb !important;
    --primary-dark: #1e40af !important;
    --primary-light: #3b82f6 !important;
    --secondary: #0ea5e9 !important;
    --accent: #06b6d4 !important;
    --white: #ffffff !important;
    --text-primary: #1e293b !important;
    --text-secondary: #334155 !important;
    --text-muted: #64748b !important;
    --success: #059669 !important;
    --warning: #d97706 !important;
    --danger: #dc2626 !important;
    --bg-gradient: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%) !important;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04) !important;
    --radius: 16px !important;
    --radius-sm: 8px !important;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

* {
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    text-decoration: none !important;
}

body {
    font-family: 'Poppins', sans-serif !important;
    background: var(--bg-gradient) !important;
    background-attachment: fixed !important;
    color: var(--text-primary) !important;
    min-height: 100vh !important;
    display: block !important;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: linear-gradient(160deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    color: white;
    height: 100vh;
    position: fixed;
    padding: 0;
    z-index: 100;
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    margin-bottom: 16px;
    background: rgba(255, 255, 255, 0.05);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    text-decoration: none !important;
    color: white;
}

.welcome-message {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px 16px;
    margin-top: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-list {
    padding: 0 12px 24px;
}

.nav-item {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border-radius: 8px;
    margin-bottom: 4px;
    color: white !important;
    text-decoration: none !important;
}

.nav-item:hover, .nav-item.active {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(4px);
}

.nav-item i {
    font-size: 18px;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    margin-left: 300px;
    padding: 32px;
    min-height: 100vh;
    transition: var(--transition);
}

/* Top Bar */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px 28px;
    border-radius: 16px;
    box-shadow: var(--shadow);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.page-title h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-dark);
    margin: 0;
}

.page-title p {
    font-size: 14px;
    color: var(--text-muted);
    margin: 4px 0 0 0;
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: var(--shadow);
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition);
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
    text-align: left;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-dark);
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
    color: #1e293b;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.95);
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 1.25rem;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        z-index: 1000;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: block;
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .mobile-menu-toggle {
        display: block !important;
    }
    
    .top-bar {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .page-title h1 {
        font-size: 1.5rem;
    }
    
    .page-title p {
        font-size: 0.875rem;
    }
    
    .card {
        margin-bottom: 1rem;
        padding: 1rem;
    }
    
    .form-control {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem;
    }
    
    .btn {
        width: 100%;
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn, .nav-item, .card {
        min-height: 44px;
        position: relative;
        overflow: hidden;
    }
    
    .btn:active, .nav-item:active, .card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}
