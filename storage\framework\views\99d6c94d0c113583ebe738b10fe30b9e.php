<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="theme-color" content="#3b82f6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CATAT">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <title><?php echo $__env->yieldContent('title', 'CATAT'); ?></title>

    <!-- Critical CSS for Mobile - Load First -->
    <style>
        /* Ensure mobile compatibility */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%) !important;
                z-index: 1000 !important;
            }
            .sidebar.open {
                transform: translateX(0) !important;
            }
            .main-content {
                margin-left: 0 !important;
                padding: 1rem !important;
            }
            .mobile-menu-toggle {
                display: block !important;
                position: fixed !important;
                top: 1rem !important;
                left: 1rem !important;
                z-index: 1001 !important;
                background: #2563eb !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                padding: 0.75rem !important;
                font-size: 1.2rem !important;
                cursor: pointer !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
                width: 50px !important;
                height: 50px !important;
            }
            .sidebar-overlay {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                background: rgba(0, 0, 0, 0.5) !important;
                z-index: 999 !important;
                display: block !important;
            }
        }
    </style>

    <!-- Flash Messages for Toast -->
    <?php if(session('success')): ?>
        <meta name="flash-success" content="<?php echo e(session('success')); ?>">
    <?php endif; ?>
    <?php if(session('error')): ?>
        <meta name="flash-error" content="<?php echo e(session('error')); ?>">
    <?php endif; ?>
    <?php if(session('warning')): ?>
        <meta name="flash-warning" content="<?php echo e(session('warning')); ?>">
    <?php endif; ?>
    <?php if(session('info')): ?>
        <meta name="flash-info" content="<?php echo e(session('info')); ?>">
    <?php endif; ?>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Mobile Fallback CSS - Always load first -->
    <link rel="stylesheet" href="<?php echo e(asset('css/mobile-fallback.css')); ?>">

    <!-- Compiled CSS Fallback -->
    <link rel="stylesheet" href="<?php echo e(asset('build/assets/app-MR6zQdOP.css')); ?>">

    <!-- Force load Vite assets -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <script src="<?php echo e(asset('js/toast.js')); ?>" defer></script>
    <script src="<?php echo e(asset('js/mobile.js')); ?>" defer></script>

    <!-- Complete CSS Embedded for Mobile Compatibility -->
    <style>
        /* CSS Variables */
        :root {
            --primary: #2563eb;
            --primary-dark: #1e40af;
            --primary-light: #3b82f6;
            --secondary: #0ea5e9;
            --accent: #06b6d4;
            --white: #ffffff;
            --light: #f0f9ff;
            --gray-light: #f1f5f9;
            --gray: #94a3b8;
            --gray-dark: #475569;
            --text-primary: #1e293b;
            --text-secondary: #334155;
            --text-muted: #64748b;
            --success: #059669;
            --warning: #d97706;
            --danger: #dc2626;
            --bg-gradient: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%);
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.12), 0 4px 10px rgba(0, 0, 0, 0.06);
            --radius: 16px;
            --radius-sm: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* CRITICAL MOBILE STYLES - FORCE LOAD */
        * {
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
            text-decoration: none !important;
        }

        body {
            font-family: 'Poppins', sans-serif !important;
            background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%) !important;
            background-attachment: fixed !important;
            color: #1e293b !important;
            min-height: 100vh !important;
            line-height: 1.6 !important;
        }

        /* FORCE MOBILE LAYOUT */
        @media (max-width: 768px) {
            body {
                background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%) !important;
                font-family: 'Poppins', sans-serif !important;
                margin: 0 !important;
                padding: 0 !important;
            }
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: linear-gradient(160deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
            color: white;
            height: 100vh;
            position: fixed;
            padding: 0;
            z-index: 100;
            transition: transform 0.3s ease;
            overflow-y: auto;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
            margin-bottom: 16px;
            background: rgba(255, 255, 255, 0.05);
        }

        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            text-decoration: none !important;
            color: white;
        }

        .logo img {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .welcome-message {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-list {
            padding: 0 12px 24px;
            list-style: none;
        }

        .nav-item {
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border-radius: 8px;
            margin-bottom: 4px;
            color: white !important;
            text-decoration: none !important;
            position: relative;
            overflow: hidden;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-item i {
            font-size: 18px;
            width: 20px;
            text-align: center;
            transition: var(--transition);
        }

        .nav-item:hover i {
            transform: scale(1.1);
        }

        /* Main Content */
        .main-content {
            margin-left: 300px;
            padding: 32px;
            min-height: 100vh;
            transition: var(--transition);
        }

        /* Top Bar */
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 28px;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-title h1 {
            font-size: 28px;
            font-weight: 600;
            color: var(--primary-dark);
            margin: 0;
        }

        .page-title p {
            font-size: 14px;
            color: var(--text-muted);
            margin: 4px 0 0 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-details {
            text-align: right;
        }

        .user-details .name {
            font-weight: 600;
            color: var(--primary-dark);
        }

        .user-details .role {
            font-size: 14px;
            color: var(--text-muted);
        }

        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        /* Cards */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        /* Forms */
        .form-group {
            margin-bottom: 1rem;
            text-align: left;
        }

        .form-group label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-dark);
            margin-bottom: 8px;
        }

        .form-control, input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            transition: var(--transition);
            color: #1e293b;
            font-family: 'Poppins', sans-serif;
        }

        .form-control:focus, input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 0.95);
            transform: translateY(-1px);
        }

        /* Buttons */
        .btn, button {
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: var(--transition);
            font-size: 14px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none !important;
            font-family: 'Poppins', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .btn-primary, button[type="submit"] {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-primary:hover, button[type="submit"]:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: rgba(100, 116, 139, 0.1);
            color: var(--text-primary);
            border: 2px solid rgba(100, 116, 139, 0.2);
        }

        .btn-secondary:hover {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        /* Mobile Menu Toggle */
        .mobile-menu-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            font-size: 1.25rem;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .mobile-menu-toggle:hover {
            background: var(--primary-dark);
            transform: scale(1.05);
        }

        /* Auth Styles */
        .auth-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            padding: 2rem 0;
            min-height: 100vh;
        }

        .auth-card {
            background: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
            text-align: center;
            color: var(--text-primary);
        }

        .auth-card h2 {
            font-size: 1.875rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .auth-card p {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0;
            margin-bottom: 1.5rem;
        }

        .auth-card .link {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease-in-out;
        }

        .auth-card .link:hover {
            color: var(--secondary);
        }

        /* Role Selection */
        .role-selection {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .btn-role {
            flex-grow: 1;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            background-color: #ffffff;
            color: var(--text-primary);
            font-weight: 500;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            text-align: center;
        }

        .btn-role[data-role="manager"],
        .btn-role[data-role="employee"],
        .btn-role[data-role="director"] {
            background-color: #93c5fd;
            color: #1e3a8a;
            border-color: #93c5fd;
        }

        .btn-role[data-role="manager"]:hover,
        .btn-role[data-role="employee"]:hover,
        .btn-role[data-role="director"]:hover {
            background-color: #bfdbfe;
            border-color: #bfdbfe;
        }

        .btn-role.selected {
            background-color: #3b82f6 !important;
            color: white !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 8px rgba(59, 130, 246, 0.7);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                display: block;
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
            }

            .mobile-menu-toggle {
                display: block !important;
            }

            .top-bar {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .page-title h1 {
                font-size: 1.5rem;
            }

            .page-title p {
                font-size: 0.875rem;
            }

            .user-info {
                order: -1;
                justify-content: center;
            }

            .card {
                margin-bottom: 1rem;
                padding: 1rem;
                border-radius: 12px;
            }

            .form-control, input, select, textarea {
                font-size: 16px; /* Prevent zoom on iOS */
                padding: 0.75rem;
            }

            .btn, button {
                width: 100%;
                padding: 0.875rem 1rem;
                font-size: 0.875rem;
                justify-content: center;
            }

            .auth-card {
                margin: 1rem;
                padding: 1.5rem;
            }

            .role-selection {
                flex-direction: column;
                gap: 0.75rem;
            }

            .btn-role {
                width: 100%;
            }

            .container {
                padding: 0 10px;
                max-width: 100%;
            }

            body {
                overflow-x: hidden;
            }
        }

        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
            .btn, .nav-item, .card, button {
                min-height: 44px;
                position: relative;
                overflow: hidden;
            }

            .btn:active, .nav-item:active, .card:active, button:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }

            .form-control, input, select, textarea {
                min-height: 44px;
                font-size: 16px;
            }
        }

        /* Utilities */
        .w-full { width: 100%; }
        .text-center { text-align: center; }
        .d-none { display: none; }
        .text-danger { color: var(--danger); }
        .mt-1 { margin-top: 0.25rem; }
        .mb-4 { margin-bottom: 1rem; }

        /* Links */
        a {
            color: var(--primary);
            text-decoration: none !important;
        }

        a:hover {
            color: var(--primary-dark);
            text-decoration: none !important;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</head>
<body>
    <!-- Mobile Menu Toggle - Always Present -->
    <button class="mobile-menu-toggle" id="mobile-menu-toggle" style="display: none;">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar Navigation -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <a href="<?php echo e(route('login')); ?>" class="logo">
                <img src="<?php echo e(asset('logo.png')); ?>" alt="CATAT Logo" style="height: 160px; width: auto;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display: none; color: white; font-size: 24px; font-weight: 700; text-align: center; padding: 20px;">
                    <i class="fas fa-clipboard-list" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
                    CATAT
                </div>
            </a>

            <!-- Welcome Message -->
            <?php if(auth()->check()): ?>
            <div class="welcome-message">
                <div style="color: white; font-size: 13px; font-weight: 600; text-shadow: 0 1px 2px rgb(171, 161, 161); line-height: 1.4; text-align: center;">
                    Selamat Datang<br>
                    <span style="font-size: 15px; font-weight: 700;"><?php echo e(Auth::user()->name); ?></span><br>
                    <span style="font-size: 11px; opacity: 0.9;">
                        Di Website
                        <?php if(Auth::user()->role === 'director'): ?>
                            Direktur
                        <?php elseif(Auth::user()->role === 'manager'): ?>
                            Manajer
                        <?php elseif(Auth::user()->role === 'employee'): ?>
                            Pegawai
                        <?php else: ?>
                            <?php echo e(ucfirst(Auth::user()->role)); ?>

                        <?php endif; ?>
                    </span>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <ul class="nav-list">
            <?php if(auth()->check() && auth()->user()->isDirector()): ?>
                <a href="<?php echo e(route('director.dashboard')); ?>" class="nav-item <?php echo e(request()->routeIs('director.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="<?php echo e(route('director.branches.index')); ?>" class="nav-item <?php echo e(request()->routeIs('director.branches.*') ? 'active' : ''); ?>">
                    <i class="fas fa-store"></i>
                    <span class="nav-text">Manajemen Cabang</span>
                </a>
                <a href="<?php echo e(route('director.users.index')); ?>" class="nav-item <?php echo e(request()->routeIs('director.users.*') ? 'active' : ''); ?>">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="<?php echo e(route('director.reports.integrated')); ?>" class="nav-item <?php echo e(request()->routeIs('director.reports.integrated') ? 'active' : ''); ?>">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            <?php elseif(auth()->check() && auth()->user()->isManager()): ?>
                <a href="<?php echo e(route('manager.dashboard')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="<?php echo e(route('manager.products.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.products.*') ? 'active' : ''); ?>">
                    <i class="fas fa-box"></i>
                    <span class="nav-text">Manajemen Produk</span>
                </a>
                <a href="<?php echo e(route('manager.categories.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.categories.*') ? 'active' : ''); ?>">
                    <i class="fas fa-tags"></i>
                    <span class="nav-text">Manajemen Kategori</span>
                </a>
                <a href="<?php echo e(route('manager.employees.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.employees.*') ? 'active' : ''); ?>">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Manajemen Akun</span>
                </a>
                <a href="<?php echo e(route('manager.returns.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.returns.*') ? 'active' : ''); ?>">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Persetujuan Retur</span>
                </a>
                <a href="<?php echo e(route('manager.damaged-stock.index')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.damaged-stock.*') ? 'active' : ''); ?>">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span class="nav-text">Barang Rusak</span>
                </a>
                <a href="<?php echo e(route('manager.reports.integrated')); ?>" class="nav-item <?php echo e(request()->routeIs('manager.reports.integrated') ? 'active' : ''); ?>">
                    <i class="fas fa-layer-group"></i>
                    <span class="nav-text">Laporan Terintegrasi</span>
                </a>
            <?php elseif(auth()->check() && auth()->user()->isEmployee()): ?>
                <a href="<?php echo e(route('employee.dashboard')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-home"></i>
                    <span class="nav-text">Beranda</span>
                </a>
                <a href="<?php echo e(route('employee.transactions.create')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.transactions.*') ? 'active' : ''); ?>">
                    <i class="fas fa-cash-register"></i>
                    <span class="nav-text">Transaksi Penjualan</span>
                </a>
                <a href="<?php echo e(route('employee.returns.index')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.returns.*') ? 'active' : ''); ?>">
                    <i class="fas fa-undo"></i>
                    <span class="nav-text">Retur Barang</span>
                </a>
                <a href="<?php echo e(route('employee.stocks.index')); ?>" class="nav-item <?php echo e(request()->routeIs('employee.stocks.*') ? 'active' : ''); ?>">
                    <i class="fas fa-boxes"></i>
                    <span class="nav-text">Stok Produk</span>
                </a>
            <?php endif; ?>

            <a href="<?php echo e(route('logout')); ?>" class="nav-item" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                <i class="fas fa-sign-out-alt"></i>
                <span class="nav-text">Keluar</span>
            </a>
            <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                <?php echo csrf_field(); ?>
            </form>
        </ul>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        
        
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    
    <script>
        // Mobile Navigation and Touch Interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuToggle = document.createElement('button');
            mobileMenuToggle.className = 'mobile-menu-toggle';
            mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';

            // Add mobile menu toggle to body
            document.body.appendChild(mobileMenuToggle);

            // Show mobile menu toggle on mobile devices
            function checkMobile() {
                if (window.innerWidth <= 768) {
                    mobileMenuToggle.style.display = 'block';
                } else {
                    mobileMenuToggle.style.display = 'none';
                    // Close sidebar if open
                    const sidebar = document.querySelector('.sidebar');
                    if (sidebar) {
                        sidebar.classList.remove('open');
                    }
                    // Remove overlay if exists
                    const overlay = document.querySelector('.sidebar-overlay');
                    if (overlay) {
                        overlay.remove();
                    }
                }
            }

            // Initial check
            checkMobile();

            // Check on resize
            window.addEventListener('resize', checkMobile);

            // Mobile menu toggle functionality
            mobileMenuToggle.addEventListener('click', function() {
                const sidebar = document.querySelector('.sidebar');
                if (!sidebar) return;

                const isOpen = sidebar.classList.contains('open');

                if (isOpen) {
                    // Close sidebar
                    sidebar.classList.remove('open');
                    mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';

                    // Remove overlay
                    const overlay = document.querySelector('.sidebar-overlay');
                    if (overlay) {
                        overlay.remove();
                    }
                } else {
                    // Open sidebar
                    sidebar.classList.add('open');
                    mobileMenuToggle.innerHTML = '<i class="fas fa-times"></i>';

                    // Create overlay
                    const overlay = document.createElement('div');
                    overlay.className = 'sidebar-overlay';

                    // Close sidebar when overlay is clicked
                    overlay.addEventListener('click', function() {
                        sidebar.classList.remove('open');
                        mobileMenuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                        overlay.remove();
                    });

                    document.body.appendChild(overlay);
                }
            });

            // Touch feedback for buttons
            const buttons = document.querySelectorAll('.btn, .nav-item, .card, button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function(e) {
                    this.style.transform = 'scale(0.98)';
                    this.style.opacity = '0.8';
                    this.style.transition = 'all 0.1s ease';
                });

                button.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                        this.style.opacity = '';
                        this.style.transition = '';
                    }, 150);
                });
            });

            // Improve form input focus on mobile
            const inputs = document.querySelectorAll('.form-control, input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    // Scroll input into view on mobile
                    if (window.innerWidth <= 768) {
                        setTimeout(() => {
                            this.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }, 300);
                    }
                });
            });
        });

        // Auto-refresh CSRF token every 30 minutes to prevent expiration
        setInterval(function() {
            fetch('/csrf-token')
                .then(response => response.json())
                .then(data => {
                    const metaTag = document.querySelector('meta[name="csrf-token"]');
                    if (metaTag) {
                        metaTag.setAttribute('content', data.token);
                    }
                    // Update all CSRF token inputs
                    document.querySelectorAll('input[name="_token"]').forEach(input => {
                        input.value = data.token;
                    });
                })
                .catch(error => {
                    console.log('CSRF token refresh failed:', error);
                });
        }, 30 * 60 * 1000); // 30 minutes

        // Handle logout form submission with better error handling
        document.addEventListener('DOMContentLoaded', function() {
            const logoutForm = document.getElementById('logout-form');
            if (logoutForm) {
                logoutForm.addEventListener('submit', function(e) {
                    // If CSRF token is missing or expired, redirect to login
                    const csrfTokenInput = logoutForm.querySelector('input[name="_token"]');
                    if (!csrfTokenInput || !csrfTokenInput.value) {
                        e.preventDefault();
                        window.location.href = '<?php echo e(route("login")); ?>';
                        return false;
                    }
                });
            }

            // Handle logout link click with fallback
            const logoutLink = document.querySelector('a[href="<?php echo e(route("logout")); ?>"]');
            if (logoutLink) {
                logoutLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = document.getElementById('logout-form');
                    if (form) {
                        form.submit();
                    } else {
                        // Fallback: redirect to logout GET route
                        window.location.href = '<?php echo e(route("logout.get")); ?>';
                    }
                });
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\KULIAH\tugas_akhir - Copy\resources\views/layouts/app.blade.php ENDPATH**/ ?>