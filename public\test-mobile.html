<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="theme-color" content="#3b82f6">
    <title>Test Mobile CSS</title>
    
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1e40af;
            --bg-gradient: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 25%, #7dd3fc 50%, #38bdf8 75%, #0ea5e9 100%);
            --text-primary: #1e293b;
            --text-muted: #64748b;
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: var(--bg-gradient);
            background-attachment: fixed;
            color: var(--text-primary);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: var(--shadow);
            padding: 2rem;
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            color: var(--primary-dark);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        p {
            color: var(--text-muted);
            margin-bottom: 1.5rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-family: 'Poppins', sans-serif;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .form-group {
            margin-bottom: 1rem;
            text-align: left;
        }
        
        label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--primary-dark);
        }
        
        input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Poppins', sans-serif;
            transition: all 0.3s ease;
        }
        
        input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .mobile-menu-toggle {
            position: fixed;
            top: 1rem;
            left: 1rem;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            font-size: 1.25rem;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            z-index: 1001;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .test-card {
                padding: 1.5rem;
            }
            
            h1 {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <button class="mobile-menu-toggle">
        <i class="fas fa-bars"></i>
    </button>
    
    <div class="test-card">
        <h1><i class="fas fa-mobile-alt"></i> Test Mobile CSS</h1>
        <p>Jika Anda melihat styling yang bagus dengan background gradient biru, berarti CSS sudah ter-load dengan benar!</p>
        
        <div class="form-group">
            <label for="test-input">Test Input:</label>
            <input type="text" id="test-input" placeholder="Ketik sesuatu...">
        </div>
        
        <button class="btn" onclick="alert('CSS dan JavaScript bekerja!')">
            <i class="fas fa-check"></i> Test Button
        </button>
        
        <p style="margin-top: 1rem; font-size: 0.875rem;">
            Jika tombol ini berfungsi dan terlihat bagus, berarti website Anda siap untuk mobile!
        </p>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Test touch feedback
            const btn = document.querySelector('.btn');
            btn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });
            
            btn.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
            
            // Test mobile menu toggle
            const toggle = document.querySelector('.mobile-menu-toggle');
            toggle.addEventListener('click', function() {
                if (this.innerHTML.includes('bars')) {
                    this.innerHTML = '<i class="fas fa-times"></i>';
                    alert('Menu terbuka! (Ini hanya test)');
                } else {
                    this.innerHTML = '<i class="fas fa-bars"></i>';
                    alert('Menu tertutup! (Ini hanya test)');
                }
            });
        });
    </script>
</body>
</html>
